# 🔍 فحص OAuth Consent Screen - السبب المحتمل لفشل Google Sign-In

## 🎯 المشكلة المحتملة

بما أن بصمة SHA-1 صحيحة ومطابقة، المشكلة الأكثر احتمالاً هي **OAuth Consent Screen** في وضع "Testing" بدلاً من "In production".

## ✅ الحل المطلوب

### 1. فحص OAuth Consent Screen

1. اذهب إلى: https://console.cloud.google.com
2. اختر مشروع `test-5c820`
3. انتقل إلى **APIs & Services** → **OAuth consent screen**
4. تحقق من:

#### أ) Publishing Status
- ✅ **يجب أن يكون**: "In production"
- ❌ **إذا كان**: "Testing" - هذا سبب المشكلة!

#### ب) App Information
- **App name**: Busaty - School
- **User support email**: بريد إلكتروني صحيح
- **Developer contact information**: بريد إلكتروني صحيح

#### ج) Scopes
- ✅ `../auth/userinfo.email`
- ✅ `../auth/userinfo.profile`
- ✅ `openid`

### 2. إذا كان في وضع "Testing"

إذا كان OAuth Consent Screen في وضع "Testing":

1. انقر على **"PUBLISH APP"**
2. اختر **"Make app available to the general public"**
3. أكد النشر

### 3. فحص Google Sign-In API

1. في نفس Google Cloud Console
2. انتقل إلى **APIs & Services** → **Enabled APIs & services**
3. تأكد من تفعيل:
   - ✅ **Google Sign-In API**
   - ✅ **Google+ API** (إذا كان متاحاً)

## 🚨 أسباب أخرى محتملة

### 1. Package Name مختلف
- تأكد من أن Package Name في Firebase هو: `com.busaty.school`
- تحقق من `android/app/build.gradle`:

```gradle
android {
    defaultConfig {
        applicationId "com.busaty.school"
    }
}
```

### 2. Google Services Plugin
- تأكد من أن `google-services` plugin مفعل في `android/app/build.gradle`:

```gradle
plugins {
    id 'com.google.gms.google-services'
}
```

### 3. Dependencies صحيحة
- تحقق من `pubspec.yaml`:

```yaml
dependencies:
  google_sign_in: ^6.1.5
  firebase_auth: ^4.15.3
  firebase_core: ^2.24.2
```

## 🔧 خطوات التشخيص

### 1. فحص Logs في Play Console

1. اذهب إلى Google Play Console
2. انتقل إلى **Quality** → **Android vitals** → **Crashes & ANRs**
3. ابحث عن أخطاء متعلقة بـ Google Sign-In

### 2. اختبار Internal Testing

1. ارفع App Bundle جديد إلى **Internal Testing**
2. اختبر Google Sign-In قبل النشر في Production
3. تحقق من Logs في الجهاز

### 3. مسح البيانات

1. احذف التطبيق من الجهاز
2. امسح بيانات Google Play Services
3. أعد تثبيت التطبيق من Play Store
4. جرب Google Sign-In مرة أخرى

## 📋 Checklist سريع

- [ ] OAuth Consent Screen في وضع "In production"
- [ ] Google Sign-In API مفعل في Google Cloud Console
- [ ] Package Name صحيح: `com.busaty.school`
- [ ] SHA-1 fingerprint صحيحة (✅ مؤكدة)
- [ ] google-services.json محدث
- [ ] Google Services plugin مفعل

## 🎯 التوقع

بعد تطبيق هذه الخطوات، خاصة تغيير OAuth Consent Screen إلى "In production"، يجب أن يعمل Google Sign-In بنجاح في Play Store builds.

## 📞 إذا استمرت المشكلة

1. تحقق من Firebase Authentication settings
2. تأكد من تفعيل Google provider في Firebase Auth
3. راجع Firebase project permissions
4. تحقق من Google Cloud Console billing (إذا كان مطلوباً)
