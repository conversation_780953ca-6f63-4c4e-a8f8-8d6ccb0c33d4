# حل مشكلة Google Sign-In بعد النشر على Google Play Store

## المشكلة
Google Sign-In يعمل في وضع التطوير (debug) وفي ملفات APK/App Bundle المحلية، لكنه يفشل بعد رفع التطبيق إلى Google Play Store.

## السبب الجذري
المشكلة تحدث بسبب عدم تطابق بصمة SHA-1 المسجلة في Firebase مع بصمة SHA-1 الفعلية التي يستخدمها Google Play App Signing.

## الحل خطوة بخطوة

### 1. الحصول على بصمة SHA-1 الصحيحة من Google Play Console

1. اذهب إلى [Google Play Console](https://play.google.com/console)
2. اختر تطبيق `com.busaty.school`
3. انتقل إلى **Setup** → **App integrity** → **App signing**
4. في قسم "App signing key certificate"، انسخ بصمة SHA-1
5. تأكد من أنها تطابق: `3C:ED:5A:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`

### 2. إضافة البصمة إلى Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اختر مشروع `test-5c820`
3. انتقل إلى **Project settings** → **General** → **Your apps**
4. اختر تطبيق Android `com.busaty.school`
5. في قسم "SHA certificate fingerprints"، تأكد من وجود البصمات التالية:
   - Debug: `8A:C7:3C:0C:FF:C2:1C:28:03:84:97:FB:8E:33:F3:7B:1D:55:D5:23`
   - Release: `A8:1E:FC:57:F5:B0:31:19:96:39:60:40:BD:FE:A3:7C:CD:04:CD:8E`
   - **Play Store: `3C:ED:5A:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`** (الأهم)

### 3. تحديث google-services.json

1. بعد إضافة البصمة في Firebase، انقر على "Download google-services.json"
2. استبدل الملف الحالي في `android/app/google-services.json`
3. تأكد من أن الملف الجديد يحتوي على Client ID للبصمة الجديدة

### 4. التحقق من OAuth Consent Screen

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com)
2. اختر مشروع `test-5c820`
3. انتقل إلى **APIs & Services** → **OAuth consent screen**
4. تأكد من:
   - Publishing status: "In production"
   - Scopes تتضمن: `email`, `profile`
   - معلومات التطبيق مكتملة (App name, User support email, Developer contact)

### 5. تنظيف وإعادة البناء

```bash
flutter clean
flutter pub get
flutter build appbundle --release
```

### 6. اختبار التطبيق

1. ارفع App Bundle الجديد إلى Google Play Console
2. انشر التحديث على Internal Testing أولاً
3. اختبر Google Sign-In من Internal Testing
4. إذا نجح، انشر على Production

## نصائح مهمة

### تأكد من استخدام الإعداد الصحيح في الكود

الكود الحالي يستخدم الإعداد الصحيح:

```dart
final GoogleSignIn googleSignIn = GoogleSignIn(
  scopes: ['email', 'profile'],
  signInOption: SignInOption.standard,
  // لا تحدد clientId - سيتم أخذه من google-services.json تلقائياً
);
```

### فهم الفرق بين المفاتيح

- **Upload Key**: المفتاح الذي تستخدمه لتوقيع التطبيق قبل الرفع
- **App Signing Key**: المفتاح الذي يستخدمه Google لتوقيع التطبيق النهائي
- **المهم**: Firebase يجب أن يحتوي على بصمة App Signing Key، ليس Upload Key

### استكشاف الأخطاء

إذا استمرت المشكلة:

1. تأكد من أن Package Name صحيح: `com.busaty.school`
2. تحقق من أن OAuth Consent Screen في وضع "In production"
3. تأكد من أن Google Sign-In API مفعل في Google Cloud Console
4. امسح بيانات التطبيق وأعد تثبيته من Play Store

## الملفات المحدثة

- `lib/config/google_signin_config.dart`: محدث ليعكس التكوين الصحيح
- `android/app/google-services.json`: يجب تحديثه من Firebase Console

## التحقق من النجاح

بعد تطبيق هذه الخطوات، يجب أن يعمل Google Sign-In بنجاح في:
- ✅ Debug builds
- ✅ Release builds محلياً
- ✅ Play Store builds (الهدف الأساسي)
