# 🚨 إصلاح عاجل - بصمة SHA-1 الصحيحة

## ❌ المشكلة المكتشفة

من خلال الصور المرسلة، تبين أن:

**البصمة في Google Play Console**: `3C:FD:A5:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`

**البصمة في google-services.json**: `3C:ED:5A:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`

⚠️ **هناك اختلاف في الأحرف**: `FD` vs `ED` و `A5` vs `5A`

## ✅ الحل الفوري

### الخطوة 1: إضافة البصمة الصحيحة إلى Firebase

1. اذهب إلى Firebase Console: https://console.firebase.google.com
2. اختر مشروع `test-5c820`
3. انتقل إلى **Project settings** → **General**
4. اختر تطبيق Android `com.busaty.school`
5. في قسم "SHA certificate fingerprints"، انقر على **"Add fingerprint"**
6. أضف البصمة الصحيحة: `3C:FD:A5:0A:3B:31:26:2B:0D:C1:AF:26:B8:5E:C2:84:DA:66:B4:67`
7. احفظ التغييرات

### الخطوة 2: تنزيل google-services.json الجديد

1. بعد إضافة البصمة، انقر على **"Download google-services.json"**
2. استبدل الملف الحالي في `android/app/google-services.json`

### الخطوة 3: إعادة البناء والاختبار

```bash
flutter clean
flutter pub get
flutter build appbundle --release
```

### الخطوة 4: رفع التحديث إلى Play Store

1. ارفع App Bundle الجديد إلى Google Play Console
2. اختبر في Internal Testing أولاً
3. تأكد من عمل Google Sign-In

## 🎯 التوقعات

بعد تطبيق هذا الإصلاح، يجب أن يعمل Google Sign-In بنجاح في:
- ✅ Debug builds
- ✅ Release builds محلياً
- ✅ **Play Store builds** (الهدف الأساسي)

## 📋 ملاحظات مهمة

1. **لا تحذف البصمات الأخرى**: أبق البصمات الموجودة (Debug و Release) واضف الجديدة فقط
2. **تأكد من OAuth Consent Screen**: يجب أن يكون في وضع "In production"
3. **اختبر تدريجياً**: Internal Testing أولاً، ثم Production

## 🔍 للتحقق من النجاح

بعد تطبيق الإصلاح، يجب أن ترى في google-services.json الجديد:

```json
{
  "client_id": "...",
  "client_type": 1,
  "android_info": {
    "package_name": "com.busaty.school",
    "certificate_hash": "3cfda50a3b31262b0dc1af26b85ec284da66b467"
  }
}
```

هذا هو الإصلاح الدقيق المطلوب لحل مشكلة Google Sign-In نهائياً!
