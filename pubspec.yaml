name: bus
description: A new Flutter project.


publish_to: "none"
version: 2.0.9+65

environment:
  sdk: ^3.5.4


dependencies:
  flutter:
    sdk: flutter
  firebase_auth: ^4.15.3
  google_sign_in: ^6.2.0
  firebase_core: ^2.24.2
  cupertino_icons: ^1.0.8
  shared_preferences: 2.3.5
  flutter_screenutil: ^5.9.3
  easy_localization: ^3.0.7
  google_maps_flutter: ^2.10.0
  easy_localization_loader: ^2.0.2
  shimmer: ^3.0.0
  fluttertoast: 8.2.10
  cached_network_image: ^3.4.1
  flutter_rating_bar: ^4.0.1
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  pin_code_fields: ^8.0.1
  flutter_svg: 2.0.17
  image_picker: ^1.1.2
  equatable: ^2.0.7
  badges: ^3.1.2
  file_picker: 8.1.7
  svg_path_parser: ^1.1.2
  touchable: ^1.0.2
  dio: ^5.7.0
  logger: ^2.5.0
  geolocator: ^10.1.0
  flutter_datetime_picker_plus: ^2.2.0
  syncfusion_flutter_xlsio:
  open_file: ^3.5.10
  path_provider: ^2.1.5
  socket_io_client: ^2.0.3
  share_plus: ^7.2.1
  flutter_local_notifications: ^18.0.1
  carousel_slider: ^5.0.0
  open_street_map_search_and_pick: ^0.1.1
  get_it: 8.0.3
  intl:
  geocoding: ^3.0.0
  connectivity_plus:
  internet_connection_checker: 3.0.1
  app_links:
  flutter_gcaptcha_v3:
  mailer: ^6.0.1
  url_launcher: ^6.3.1
  flutter_inapp_purchase: 5.6.2
  in_app_purchase: ^3.2.1
  firebase_messaging: ^14.7.10
  json_annotation: ^4.9.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  json_serializable: 6.9.2
  flutter_lints: ^5.0.0
  build_runner: ^2.4.8


flutter_launcher_icons:
  android: "ic_launcher"
  ios: true
  image_path: "assets/images/logo.png"
  min_sdk_android: 21

flutter:

  uses-material-design: true

  assets:
    - assets/translations/
    - assets/images/
